<template>
  <view class="interpretation-page">
    <view v-if="interpretationDetail">
      
      <!-- 视频播放器 -->
      <view v-if="interpretationDetail.video_url" class="video-player-section">
        <video 
          :src="interpretationDetail.video_url"
          :poster="videoPoster"
          controls
          class="video-player"
          @play="recordVideoPlay"
        ></video>
      </view>

      <!-- 内容信息卡片 -->
      <view class="info-card">
        <view class="title">{{ interpretationDetail.title }}</view>
        <view class="meta-info">
          <text>发布时间：{{ formatDate(interpretationDetail.publish_date) }}</text>
          <text>观看：{{ interpretationDetail.view_count || 0 }}次</text>
        </view>
        
        <view v-if="interpretationDetail.content" class="content-body">
          <rich-text :nodes="interpretationDetail.content"></rich-text>
        </view>

        <view v-if="interpretationDetail.attachment_url" class="attachment-section">
          <view class="attachment-title">附件：</view>
          <view class="attachment-item" @click="openAttachment">
            <text class="attachment-icon">📎</text>
            <text class="attachment-name">{{ interpretationDetail.attachment_name || '点击查看附件' }}</text>
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="action-bar-footer">
        <button @click="toggleLike" class="action-btn" :class="{ active: isLiked }">
          <text class="icon">👍</text>
          <text class="action-text">{{ interpretationDetail.like_count > 0 ? interpretationDetail.like_count : '点赞' }}</text>
        </button>
        <button @click="toggleCollect" class="action-btn" :class="{ active: isCollected }">
          <text class="icon">{{ isCollected ? '⭐' : '☆' }}</text>
          <text class="action-text">{{ interpretationDetail.collect_count > 0 ? interpretationDetail.collect_count : '收藏' }}</text>
        </button>
        <button open-type="share" class="action-btn">
          <text class="icon">📤</text>
          <text class="action-text">{{ interpretationDetail.forward_count > 0 ? interpretationDetail.forward_count : '分享' }}</text>
        </button>
      </view>
      
    </view>

    <view v-else class="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      interpretationDetail: null,
      interpretationId: null,
      isLiked: false,
      isCollected: false,
      videoPoster: '' // 可设置一个默认封面
    }
  },

  onLoad(options) {
    this.interpretationId = options.id
    if (this.interpretationId) {
      this.loadInterpretationDetail()
    }
  },

  methods: {
    async loadInterpretationDetail() {
      try {
        const res = await api.getInterpretationDetail(this.interpretationId)
        this.interpretationDetail = res.data
        this.videoPoster = this.interpretationDetail.cover_img || '/static/images/video-poster.jpg';

        // 详情加载成功后
        this.recordView()
        this.checkStatus()
      } catch (error) {
        console.error('加载政策解读详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    async checkStatus() {
      const token = uni.getStorageSync('token');
      if (!token) return;

      try {
        const params = {
          item_type: 'interpretation',
          item_id: parseInt(this.interpretationId)
        };
        const [likeRes, collectRes] = await Promise.all([
          api.checkInteractionStatus({ ...params, actions: ['like'] }),
          api.checkCollectionStatus(params)
        ]);

        if (likeRes.data.like) {
          this.isLiked = true;
        }
        if (collectRes.data.is_collected) {
          this.isCollected = true;
        }
      } catch (error) {
        console.error('检查状态失败:', error);
      }
    },

    async recordView() {
      const token = uni.getStorageSync('token');
      if (!token) {
        return; // 未登录，不记录
      }
      try {
        await api.recordView({
          item_type: 'interpretation',
          item_id: parseInt(this.interpretationId)
        });
        if (this.interpretationDetail) {
          this.interpretationDetail.view_count++;
        }
      } catch (error) {
        console.error('记录浏览失败:', error);
      }
    },

    recordVideoPlay() {
      // 可以在此添加更详细的视频播放记录逻辑
      console.log('视频开始播放');
    },

    async toggleLike() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('点赞');
        return;
      }
      try {
        const res = await api.toggleInteraction({
          action: 'like',
          item_type: 'interpretation',
          item_id: parseInt(this.interpretationId)
        });
        
        this.isLiked = res.data.action === 'added';
        this.interpretationDetail.like_count = res.data.current_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('点赞操作失败:', error);
      }
    },

    async toggleCollect() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('收藏');
        return;
      }
      try {
        const res = await api.toggleCollection({
          item_type: 'interpretation',
          item_id: parseInt(this.interpretationId)
        });
        
        this.isCollected = res.data.action === 'added';
        this.interpretationDetail.collect_count = res.data.current_collect_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('收藏操作失败:', error);
      }
    },
    
    openAttachment() {
      if (!this.interpretationDetail.attachment_url) return;
      uni.showLoading({ title: '正在打开附件' });
      uni.downloadFile({
        url: this.interpretationDetail.attachment_url,
        success: (res) => {
          const filePath = res.tempFilePath;
          uni.openDocument({
            filePath: filePath,
            showMenu: true,
            success: () => {
              uni.hideLoading();
            },
            fail: (err) => {
              uni.hideLoading();
              uni.showToast({
                title: '打开附件失败',
                icon: 'none'
              });
              console.error('打开附件失败', err);
            }
          });
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({
            title: '下载附件失败',
            icon: 'none'
          });
          console.error('下载附件失败', err);
        }
      });
    },

    promptLogin(action) {
      uni.showModal({
        title: '请先登录',
        content: `登录后才能${action}哦`,
        success: (res) => {
          if (res.confirm) {
            uni.switchTab({
              url: '/pages/profile/index'
            });
          }
        }
      });
    },

    onShareAppMessage() {
      return {
        title: this.interpretationDetail.title,
        path: `/pages/interpretation/detail?id=${this.interpretationId}`,
        imageUrl: this.videoPoster
      };
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = ('0' + (date.getMonth() + 1)).slice(-2)
      const day = ('0' + date.getDate()).slice(-2)
      return `${year}年${month}月${day}日`
    }
  }
}
</script>

<style lang="scss" scoped>
.interpretation-page {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding-bottom: 160rpx; // 为底部按钮留出空间
}

.video-player-section {
  width: 100%;
  height: 420rpx;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: 100%;
}

.info-card {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.meta-info {
  font-size: 26rpx;
  color: #999;
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.content-body {
  font-size: 30rpx;
  color: #555;
  line-height: 1.8;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.attachment-section {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.attachment-title {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #e9ecef;
  border-radius: 8rpx;
}

.attachment-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.attachment-name {
  color: #007bff;
  font-size: 28rpx;
  text-decoration: underline;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}

.action-bar-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.98);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  flex: 1;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  color: #666;
  transition: color 0.2s ease-in-out, transform 0.1s ease;
  
  &::after {
    border: none;
  }
  
  &.active {
    color: #DC143C;
  }
  
  .icon {
    font-size: 44rpx;
    transition: all 0.2s ease;
  }

  &.active .icon {
    transform: scale(1.1);
  }

  .action-text {
    font-size: 24rpx;
  }
}
</style> 