<template>
  <view class="consultation-page-old">
    <!-- 咨询类型选择 -->
    <view class="type-section section">
      <view class="section-title">咨询类型</view>
      <view class="type-options">
        <view 
          class="type-option" 
          :class="{ active: consultationType === 'business_consult' }"
          @click="selectType('business_consult')"
        >
          <view class="option-icon">💼</view>
          <text class="option-title">业务咨询</text>
          <text class="option-desc">一般性外汇政策咨询</text>
        </view>
        <view 
          class="type-option" 
          :class="{ active: consultationType === 'policy_demand' }"
          @click="selectType('policy_demand')"
        >
          <view class="option-icon">🏦</view>
          <text class="option-title">政策需求</text>
          <text class="option-desc">向银行机构提出政策支持与服务需求</text>
        </view>
      </view>
    </view>

    <!-- 银行选择（政策需求时显示） -->
    <view class="bank-section section" v-if="consultationType === 'policy_demand'">
      <view class="section-title">选择银行机构</view>
      <view class="bank-selector" @click="showBankPicker = true">
        <text class="bank-selected" v-if="selectedBank">{{ selectedBank.name }}</text>
        <text class="bank-placeholder" v-else>请选择银行机构</text>
        <text class="bank-arrow">></text>
      </view>
    </view>

    <!-- 咨询表单 -->
    <view class="form-section section">
      <view class="section-title">咨询信息</view>
      <view class="form-content">
        <view class="form-item">
          <text class="form-label">联系人姓名 <text class="required">*</text></text>
          <input 
            class="form-input" 
            :class="{ error: errors.contact_name }"
            v-model="formData.contact_name" 
            placeholder="请输入您的姓名"
            maxlength="20"
            @blur="validateName"
            @input="validateName"
          />
          <text class="error-text" v-if="errors.contact_name">{{ errors.contact_name }}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">联系电话 <text class="required">*</text></text>
          <input 
            class="form-input" 
            :class="{ error: errors.contact_phone }"
            v-model="formData.contact_phone" 
            placeholder="请输入联系电话"
            type="number"
            maxlength="11"
            @blur="validatePhone"
            @input="validatePhone"
          />
          <text class="error-text" v-if="errors.contact_phone">{{ errors.contact_phone }}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">咨询内容 <text class="required">*</text></text>
          <textarea 
            class="form-textarea" 
            :class="{ error: errors.content }"
            v-model="formData.content" 
            placeholder="请详细描述您的咨询内容，我们将尽快为您回复"
            maxlength="600"
            @blur="validateContent"
            @input="validateContent"
          ></textarea>
          <view class="char-count">{{ formData.content.length }}/600</view>
          <text class="error-text" v-if="errors.content">{{ errors.content }}</text>
        </view>
        
        <!-- 提交按钮移到表单内 -->
        <view class="form-submit">
          <view 
            class="submit-btn" 
            :class="{ disabled: !canSubmit }"
            @click="submitConsultation"
          >
            提交咨询
          </view>
        </view>
      </view>
    </view>

    <!-- 重庆特色提示 -->
    <view class="tip-section section cq-decoration">
      <view class="tip-header">
        <text class="tip-icon">💡</text>
        <text class="tip-title">温馨提示</text>
      </view>
      <view class="tip-content">
        <text class="tip-text">• 我们将及时回复您的咨询</text>
        <text class="tip-text">• 如需紧急咨询，请直接联系相关银行机构</text>
        <text class="tip-text">• 国家外汇管理局重庆市分局咨询热线：023-********</text>
        <text class="tip-text">• 服务时间：周一至周五（节假日除外）8:30-12:00、14:00-17:30</text>
      </view>
    </view>

    <!-- 银行选择弹窗 -->
    <view class="modal-overlay" v-if="showBankPicker" @click="showBankPicker = false">
                <view class="bank-picker" catchtap="true">
        <view class="picker-header">
          <text class="picker-title">选择银行机构</text>
          <text class="picker-close" @click="showBankPicker = false">×</text>
        </view>
        <scroll-view class="picker-list" scroll-y>
          <view 
            class="picker-item" 
            v-for="bank in bankList" 
            :key="bank.id"
            @click="selectBank(bank)"
          >
            <text class="bank-name">{{ bank.name }}</text>
            <text class="bank-contact">{{ bank.contact_person }} - {{ bank.phone }}</text>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 自定义TabBar -->
    <custom-tab-bar />
  </view>
</template>

<script>
import { api } from '@/utils/api'
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      consultationType: 'business_consult',
      selectedBank: null,
      showBankPicker: false,
      bankList: [],
      formData: {
        contact_name: '',
        contact_phone: '',
        content: ''
      },
      errors: {}
    }
  },
  computed: {
    canSubmit() {
      const { contact_name, contact_phone, content } = this.formData
      
      // 详细的字段验证
      const nameValid = contact_name.trim().length >= 2 && contact_name.trim().length <= 20
      const phoneValid = /^1[3-9]\d{9}$/.test(contact_phone.trim())
      const contentValid = content.trim().length >= 10 && content.trim().length <= 600
      
      // 银行选择验证（政策需求时必须选择银行）
      const bankValid = this.consultationType === 'business_consult' || this.selectedBank
      
      // 没有错误信息
      const noErrors = Object.values(this.errors).every(error => !error)
      
      return nameValid && phoneValid && contentValid && bankValid && noErrors
    }
  },
  onLoad() {
    this.loadBankList()
  },
  onShow() {
    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
      this.$root.$mp.page.getTabBar().$vm.updateSelected(2)
    }
  },
  methods: {
    selectType(type) {
      this.consultationType = type
      if (type === 'business_consult') {
        this.selectedBank = null
      }
      // 清除错误信息
      this.errors = {}
    },

    selectBank(bank) {
      this.selectedBank = bank
      this.showBankPicker = false
    },

    async loadBankList() {
      try {
        const res = await api.getBanks()
        this.bankList = res.data?.items || []
      } catch (error) {
        console.error('加载银行列表失败:', error)
        // 模拟数据
        this.bankList = [
          { id: 1, name: '中国银行重庆分行', contact_person: '张经理', phone: '023-********' },
          { id: 2, name: '建设银行重庆分行', contact_person: '李经理', phone: '023-********' },
          { id: 3, name: '工商银行重庆分行', contact_person: '王经理', phone: '023-********' },
          { id: 4, name: '农业银行重庆分行', contact_person: '刘经理', phone: '023-********' },
          { id: 5, name: '交通银行重庆分行', contact_person: '陈经理', phone: '023-********' }
        ]
      }
    },

    async submitConsultation() {
      // 如果按钮被禁用，直接返回
      if (!this.canSubmit) {
        return
      }
      
      // 进行全面校验
      if (!this.validateAll()) {
        uni.showToast({
          title: '请检查并完善表单信息',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '提交中...' })

        // 准备提交数据（暂时不包含用户ID）
        const submitData = {
          contact_name: this.formData.contact_name.trim(),
          contact_phone: this.formData.contact_phone.trim(),
          content: this.formData.content.trim(),
          type: this.consultationType
        }

        // 如果是政策需求，添加银行ID
        if (this.consultationType === 'policy_demand' && this.selectedBank) {
          submitData.bank_id = this.selectedBank.id
        }

        console.log('提交的数据:', submitData)
        
        // 调用API提交数据
        const response = await api.submitInquiry(submitData)
        console.log('提交响应:', response)

        uni.hideLoading()
        uni.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000
        })

        // 重置表单和错误信息
        this.formData = {
          contact_name: '',
          contact_phone: '',
          content: ''
        }
        this.errors = {}
        this.selectedBank = null
        this.consultationType = 'business_consult'

      } catch (error) {
        uni.hideLoading()
        console.error('提交咨询失败:', error)
        
        // 根据错误类型显示不同的提示
        let errorMessage = '提交失败，请重试'
        if (error.message) {
          errorMessage = error.message
        } else if (error.data && error.data.message) {
          errorMessage = error.data.message
        }
        
        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      }
    },

    validateName() {
      const name = this.formData.contact_name.trim()
      if (!name) {
        this.errors.contact_name = '请输入联系人姓名'
      } else if (name.length < 2) {
        this.errors.contact_name = '姓名至少需要2个字符'
      } else if (name.length > 20) {
        this.errors.contact_name = '姓名不能超过20个字符'
      } else {
        this.errors.contact_name = ''
      }
    },

    validatePhone() {
      const phone = this.formData.contact_phone.trim()
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phone) {
        this.errors.contact_phone = '请输入联系电话'
      } else if (!phoneRegex.test(phone)) {
        this.errors.contact_phone = '请输入正确的手机号码'
      } else {
        this.errors.contact_phone = ''
      }
    },

    validateContent() {
      const content = this.formData.content.trim()
      if (!content) {
        this.errors.content = '请输入咨询内容'
      } else if (content.length < 10) {
        this.errors.content = '咨询内容至少需要10个字符'
      } else if (content.length > 600) {
        this.errors.content = '咨询内容不能超过600个字符'
      } else {
        this.errors.content = ''
      }
    },

    validateBank() {
      if (this.consultationType === 'policy_demand' && !this.selectedBank) {
        uni.showToast({
          title: '请选择银行机构',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateAll() {
      this.validateName()
      this.validatePhone()
      this.validateContent()
      
      const hasErrors = Object.values(this.errors).some(error => error !== '')
      return !hasErrors && this.validateBank()
    }
  }
}
</script>

<style lang="scss" scoped>
.consultation-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */
  position: relative;
}

/* 顶部重庆山城风格背景 */
.consultation-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(180deg, 
    #1E90FF 0%, 
    #4A90E2 25%, 
    #6F80FF 50%, 
    rgba(111, 128, 255, 0.7) 70%, 
    rgba(175, 184, 255, 0.4) 85%, 
    rgba(223, 228, 255, 0.2) 95%, 
    transparent 100%
  );
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.consultation-page::after {
  content: '';
  position: absolute;
  top: 180rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 100'%3E%3Cpath d='M0,80 L50,75 L100,70 L150,65 L200,60 L250,65 L300,55 L350,60 L400,50 L450,55 L500,45 L550,50 L600,40 L650,45 L700,35 L750,40 L800,30 L850,35 L900,25 L950,30 L1000,20 L1050,25 L1100,15 L1150,20 L1200,10 L1200,100 L0,100 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 100rpx;
  z-index: 2;
  opacity: 0.8;
}

.section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 3;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.type-section {
  margin: 0 30rpx 30rpx;
  margin-top: 0;
}

.type-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 30rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s;
}

.type-option.active {
  border-color: #1E90FF;
  background: rgba(30, 144, 255, 0.05);
}

.option-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.option-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 12rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
}

.bank-section {
  margin: 0 30rpx 30rpx;
}

.bank-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
}

.bank-selected {
  font-size: 30rpx;
  color: #333;
}

.bank-placeholder {
  font-size: 30rpx;
  color: #999;
}

.bank-arrow {
  font-size: 28rpx;
  color: #999;
}

.form-section {
  margin: 0 30rpx 30rpx;
}

.form-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required {
  color: #1E90FF;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
  font-size: 30rpx;
  box-sizing: border-box;
  line-height: 88rpx;
}

.form-input:focus {
  border-color: #1E90FF;
  background: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
  font-size: 30rpx;
  box-sizing: border-box;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #1E90FF;
  background: #fff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.form-submit {
  margin-top: 40rpx;
  padding-top: 20rpx;
}

.submit-btn {
  width: 100%;
  padding: 32rpx 24rpx;
  background: linear-gradient(45deg, #1E90FF, #4A90E2);
  color: white;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(30, 144, 255, 0.3);
  box-sizing: border-box;
  border: none;
  cursor: pointer;
}

.submit-btn:active:not(.disabled) {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
}

.submit-btn.disabled {
  opacity: 0.5;
  background: #ccc;
  box-shadow: none;
  cursor: not-allowed;
  transform: none;
}

.tip-section {
  margin: 0 30rpx 30rpx;
}

.tip-header {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.tip-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1E90FF;
}

.tip-content {
  padding: 0 30rpx 30rpx;
}

.tip-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.bank-picker {
  width: 600rpx;
  max-height: 800rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.picker-close {
  font-size: 40rpx;
  color: #999;
}

.picker-list {
  max-height: 600rpx;
}

.picker-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-item:last-child {
  border-bottom: none;
}

.bank-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.bank-contact {
  font-size: 26rpx;
  color: #666;
}

.form-input.error,
.form-textarea.error {
  border-color: #ff4757;
  background: #fff5f5;
}

.error-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  display: block;
}
</style> 