<template>
  <view class="page flex-col">
    <view class="group_1 flex-col">
      <image
        class="image_1"
        src="/static/images/consultation/bg-header.png"
      />
      <view class="box_1 flex-row">
        <text class="text_1">咨询</text>
        <image
          class="image_2"
          src="/static/images/consultation/bg-header.png"
        />
      </view>
      <view class="box_2 flex-col">
        <view class="group_2 flex-row">
          <view class="group_3 flex-col"></view>
          <text class="text_2">咨询类型</text>
        </view>
        <view class="group_4 flex-row justify-between">
          <view 
            class="section_1 flex-row" 
            :class="{ active: consultationType === 'business_consult' }"
            @click="selectType('business_consult')"
          >
            <view class="image-text_1 flex-col justify-between">
              <image
                class="label_1"
                src="/static/images/consultation/icon-business.png"
              />
              <view class="text-group_1 flex-col justify-between">
                <text class="text_3">业务咨询</text>
                <text class="text_4">一般性外汇政策咨询</text>
              </view>
            </view>
          </view>
          <view 
            class="section_2 flex-row"
            :class="{ active: consultationType === 'policy_demand' }"
            @click="selectType('policy_demand')"
          >
            <view class="image-text_2 flex-col justify-between">
              <image
                class="label_2"
                src="/static/images/consultation/icon-policy.png"
              />
              <view class="text-group_2 flex-col justify-between">
                <text class="text_5">政策需求</text>
                <text class="text_6">向银行机构提出政策支持与服务需求</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 银行选择（政策需求时显示） -->
    <view class="bank-section" v-if="consultationType === 'policy_demand'">
      <view class="bank-selector" @click="showBankPicker = true">
        <text class="bank-selected" v-if="selectedBank">{{ selectedBank.name }}</text>
        <text class="bank-placeholder" v-else>请选择银行机构</text>
        <text class="bank-arrow">></text>
      </view>
    </view>

    <view class="group_5 flex-col">
      <view class="block_1 flex-row justify-between">
        <view class="box_3 flex-col"></view>
        <text class="text_7">咨询信息</text>
      </view>
      <view class="text-wrapper_1">
        <text class="text_8">*</text>
        <text class="text_9">联系人姓名</text>
      </view>
      <view class="block_2 flex-col justify-center">
        <input 
          class="text_10" 
          :class="{ error: errors.contact_name }"
          v-model="formData.contact_name" 
          placeholder="请输入您的姓名"
          maxlength="20"
          @blur="validateName"
          @input="validateName"
        />
        <text class="error-text" v-if="errors.contact_name">{{ errors.contact_name }}</text>
      </view>
      
      <view class="text-wrapper_3">
        <text class="text_11">*</text>
        <text class="text_12">联系电话</text>
      </view>
      <view class="block_3 flex-col justify-center">
        <input 
          class="text_13" 
          :class="{ error: errors.contact_phone }"
          v-model="formData.contact_phone" 
          placeholder="请输入您的联系电话"
          type="number"
          maxlength="11"
          @blur="validatePhone"
          @input="validatePhone"
        />
        <text class="error-text" v-if="errors.contact_phone">{{ errors.contact_phone }}</text>
      </view>
      
      <view class="text-wrapper_5">
        <text class="text_14">*</text>
        <text class="text_15">咨询内容</text>
      </view>
      <view class="text-wrapper_7 flex-col">
        <textarea 
          class="text_17" 
          :class="{ error: errors.content }"
          v-model="formData.content" 
          placeholder="请详细描述您的咨询内容，我们将尽快为您回复"
          maxlength="600"
          @blur="validateContent"
          @input="validateContent"
        ></textarea>
        <text class="text_18">{{ formData.content.length }}/600</text>
        <text class="error-text" v-if="errors.content">{{ errors.content }}</text>
      </view>
      
      <view class="text-wrapper_6 flex-col" @click="submitForm">
        <text class="text_16">提交咨询</text>
      </view>
    </view>
    
    <view class="group_6 flex-col">
      <view class="box_4 flex-row justify-between">
        <image
          class="thumbnail_1"
          src="/static/images/consultation/icon-tip.png"
        />
        <text class="text_19">温馨提示</text>
      </view>
      <text class="paragraph_1">
        ·我们将及时回复您的咨询
        <br />
        ·如需紧急咨询，请直接联系相关银行机构
        <br />
        ·国家外汇管理局重庆市分局咨询热线：023-67677161
        <br />
        ·服务时间：周一至周五（节假日除外）8:30-12:00、14:00-17:30
      </text>
    </view>
    
    <view class="group_7 flex-col">
      <image
        class="image_3"
        src="/static/images/common/divider.png"
      />
      <view class="list_1 flex-row">
        <view
          class="image-text_3 flex-col justify-between"
          v-for="(item, index) in tabBarData"
          :key="index"
          @click="switchTab(item.url)"
        >
          <image
            class="label_3"
            :src="item.active ? item.activeIcon : item.icon"
          />
          <text
            class="text-group_3"
            :style="{ color: item.active ? '#1F73FF' : '#939AA0' }"
          >{{ item.text }}</text>
        </view>
      </view>
      <image
        class="image_4"
        src="/static/images/common/safe-area.png"
      />
    </view>

    <!-- 银行选择弹窗 -->
    <view class="modal-overlay" v-if="showBankPicker" @click="showBankPicker = false">
      <view class="bank-picker" catchtap="true">
        <view class="picker-header">
          <text class="picker-title">选择银行机构</text>
          <text class="picker-close" @click="showBankPicker = false">×</text>
        </view>
        <scroll-view class="picker-list" scroll-y>
          <view 
            class="picker-item" 
            v-for="bank in bankList" 
            :key="bank.id"
            @click="selectBank(bank)"
          >
            <text class="bank-name">{{ bank.name }}</text>
            <text class="bank-contact">{{ bank.contact_person }} - {{ bank.phone }}</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
import { api } from '@/utils/api'

export default {
  data() {
    return {
      consultationType: 'business_consult',
      selectedBank: null,
      showBankPicker: false,
      bankList: [],
      formData: {
        contact_name: '',
        contact_phone: '',
        content: ''
      },
      errors: {},
      tabBarData: [
        {
          text: '首页',
          icon: '/static/images/tabbar/home-inactive.png',
          activeIcon: '/static/images/tabbar/home-active.png',
          url: '/pages/index/index',
          active: false
        },
        {
          text: '政策',
          icon: '/static/images/tabbar/policy-inactive.png',
          activeIcon: '/static/images/tabbar/policy-active.png',
          url: '/pages/policy/index',
          active: false
        },
        {
          text: '咨询',
          icon: '/static/images/tabbar/consultation-active.png',
          activeIcon: '/static/images/tabbar/consultation-active.png',
          url: '/pages/consultation/index',
          active: true
        },
        {
          text: '我的',
          icon: '/static/images/tabbar/profile-inactive.png',
          activeIcon: '/static/images/tabbar/profile-active.png',
          url: '/pages/profile/index',
          active: false
        }
      ]
    }
  },

  computed: {
    canSubmit() {
      const { contact_name, contact_phone, content } = this.formData
      
      // 详细的字段验证
      const nameValid = contact_name.trim().length >= 2 && contact_name.trim().length <= 20
      const phoneValid = /^1[3-9]\d{9}$/.test(contact_phone.trim())
      const contentValid = content.trim().length >= 10 && content.trim().length <= 600
      
      // 银行选择验证（仅政策需求时需要）
      const bankValid = this.consultationType === 'business_consult' || this.selectedBank
      
      return nameValid && phoneValid && contentValid && bankValid
    }
  },

  onLoad() {
    this.loadBankList()
  },

  methods: {
    selectType(type) {
      this.consultationType = type
      // 切换类型时重置银行选择
      if (type === 'business_consult') {
        this.selectedBank = null
      }
      // 清除错误信息
      this.errors = {}
    },

    selectBank(bank) {
      this.selectedBank = bank
      this.showBankPicker = false
    },

    async loadBankList() {
      try {
        const res = await api.getBanks()
        this.bankList = res.data?.items || []
      } catch (error) {
        console.error('加载银行列表失败:', error)
        // 模拟数据
        this.bankList = [
          { id: 1, name: '中国银行重庆分行', contact_person: '张经理', phone: '023-********' },
          { id: 2, name: '建设银行重庆分行', contact_person: '李经理', phone: '023-********' },
          { id: 3, name: '工商银行重庆分行', contact_person: '王经理', phone: '023-********' },
          { id: 4, name: '农业银行重庆分行', contact_person: '刘经理', phone: '023-********' },
          { id: 5, name: '交通银行重庆分行', contact_person: '陈经理', phone: '023-********' }
        ]
      }
    },

    switchTab(url) {
      if (url !== '/pages/consultation/index') {
        uni.switchTab({ url })
      }
    },

    async submitForm() {
      // 使用原来的 submitConsultation 逻辑
      await this.submitConsultation()
    },

    async submitConsultation() {
      // 如果按钮被禁用，直接返回
      if (!this.canSubmit) {
        return
      }
      
      // 进行全面校验
      if (!this.validateAll()) {
        uni.showToast({
          title: '请检查并完善表单信息',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '提交中...' })
        
        const submitData = {
          consultation_type: this.consultationType,
          contact_name: this.formData.contact_name.trim(),
          contact_phone: this.formData.contact_phone.trim(),
          content: this.formData.content.trim()
        }
        
        // 如果是政策需求，添加银行信息
        if (this.consultationType === 'policy_demand' && this.selectedBank) {
          submitData.bank_id = this.selectedBank.id
        }
        
        const res = await api.submitInquiry(submitData)
        
        uni.hideLoading()
        
        if (res.success) {
          uni.showModal({
            title: '提交成功',
            content: '您的咨询已提交成功，我们将尽快为您回复！',
            showCancel: false,
            success: () => {
              // 重置表单
              this.formData = {
                contact_name: '',
                contact_phone: '',
                content: ''
              }
              this.selectedBank = null
              this.consultationType = 'business_consult'
              this.errors = {}
            }
          })
        } else {
          throw new Error(res.message || '提交失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('提交咨询失败:', error)
        
        let errorMessage = '提交失败，请稍后重试'
        if (error.data && error.data.message) {
          errorMessage = error.data.message
        }
        
        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      }
    },

    validateName() {
      const name = this.formData.contact_name.trim()
      if (!name) {
        this.errors.contact_name = '请输入联系人姓名'
      } else if (name.length < 2) {
        this.errors.contact_name = '姓名至少需要2个字符'
      } else if (name.length > 20) {
        this.errors.contact_name = '姓名不能超过20个字符'
      } else {
        this.errors.contact_name = ''
      }
    },

    validatePhone() {
      const phone = this.formData.contact_phone.trim()
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phone) {
        this.errors.contact_phone = '请输入联系电话'
      } else if (!phoneRegex.test(phone)) {
        this.errors.contact_phone = '请输入正确的手机号码'
      } else {
        this.errors.contact_phone = ''
      }
    },

    validateContent() {
      const content = this.formData.content.trim()
      if (!content) {
        this.errors.content = '请输入咨询内容'
      } else if (content.length < 10) {
        this.errors.content = '咨询内容至少需要10个字符'
      } else if (content.length > 600) {
        this.errors.content = '咨询内容不能超过600个字符'
      } else {
        this.errors.content = ''
      }
    },

    validateBank() {
      // 如果是政策需求类型，必须选择银行
      if (this.consultationType === 'policy_demand' && !this.selectedBank) {
        return false
      }
      return true
    },

    validateAll() {
      this.validateName()
      this.validatePhone()
      this.validateContent()
      
      // 检查是否有错误
      const hasErrors = Object.values(this.errors).some(error => error !== '')
      return !hasErrors && this.validateBank()
    }
  }
}
</script>

<style>
@import './common.css';
@import './index.rpx.css';
</style>
