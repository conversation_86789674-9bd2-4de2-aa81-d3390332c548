.page {
  background-color: rgba(242, 245, 247, 1);
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  overflow: hidden;
}

.group_1 {
  position: relative;
  width: 750rpx;
  height: 520rpx;
  background: linear-gradient(135deg, #1F73FF 0%, #33C5FF 100%);
}

.image_1 {
  width: 750rpx;
  height: 88rpx;
}

.box_1 {
  background-color: rgba(0, 0, 0, 0.3);
  width: 750rpx;
  height: 88rpx;
  margin-bottom: 344rpx;
  align-items: center;
  justify-content: center;
}

.text_1 {
  width: 72rpx;
  height: 88rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 36rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  line-height: 88rpx;
  margin-left: 340rpx;
}

.image_2 {
  width: 168rpx;
  height: 64rpx;
  margin: 12rpx 16rpx 0 154rpx;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  position: absolute;
  left: 24rpx;
  top: 200rpx;
  width: 702rpx;
  height: 416rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.group_2 {
  width: 702rpx;
  height: 88rpx;
  align-items: center;
}

.group_3 {
  background-color: rgba(31, 115, 255, 1);
  width: 8rpx;
  height: 32rpx;
  margin: 28rpx 0 0 32rpx;
}

.text_2 {
  width: 128rpx;
  height: 88rpx;
  overflow-wrap: break-word;
  color: rgba(26, 32, 44, 1);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  line-height: 88rpx;
  margin: 0 520rpx 0 14rpx;
}

.group_4 {
  width: 636rpx;
  height: 280rpx;
  margin: 16rpx 0 32rpx 32rpx;
}

.section_1 {
  background-image: linear-gradient(
    180deg,
    rgba(49, 120, 249, 1) 0,
    rgba(51, 197, 255, 1) 100%
  );
  border-radius: 8px;
  width: 302rpx;
  height: 280rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.section_1:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(49, 120, 249, 0.3);
}

.section_2 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8px;
  width: 302rpx;
  height: 280rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.section_2:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.section_2.active {
  background-image: linear-gradient(
    180deg,
    rgba(49, 120, 249, 1) 0,
    rgba(51, 197, 255, 1) 100%
  );
  border-color: rgba(31, 115, 255, 1);
}

.section_2.active .text_5,
.section_2.active .text_6 {
  color: rgba(255, 255, 255, 1) !important;
}

.image-text_1 {
  width: 242rpx;
  height: 188rpx;
  margin: 24rpx 0 0 30rpx;
}

.label_1 {
  width: 88rpx;
  height: 88rpx;
  margin-left: 78rpx;
}

.text-group_1 {
  width: 242rpx;
  height: 84rpx;
  margin-top: 16rpx;
}

.text_3 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 38rpx;
  margin-left: 66rpx;
}

.text_4 {
  width: 242rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 36rpx;
  margin-top: 8rpx;
}

.image-text_2 {
  width: 242rpx;
  height: 224rpx;
  margin: 24rpx 0 0 30rpx;
}

.label_2 {
  width: 88rpx;
  height: 88rpx;
  margin-left: 78rpx;
}

.text-group_2 {
  width: 242rpx;
  height: 120rpx;
  margin-top: 16rpx;
}

.text_5 {
  width: 116rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(26, 32, 44, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 38rpx;
  margin-left: 64rpx;
  transition: color 0.3s ease;
}

.text_6 {
  width: 242rpx;
  height: 72rpx;
  overflow-wrap: break-word;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  line-height: 36rpx;
  margin-top: 8rpx;
  transition: color 0.3s ease;
}

/* 银行选择区域 */
.bank-section {
  margin: 20rpx 24rpx;
}

.bank-selector {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.bank-selected {
  color: rgba(26, 32, 44, 1);
  font-size: 28rpx;
}

.bank-placeholder {
  color: rgba(177, 181, 188, 1);
  font-size: 28rpx;
}

.bank-arrow {
  color: rgba(147, 152, 160, 1);
  font-size: 32rpx;
}

/* 表单区域 */
.group_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  position: relative;
  width: 702rpx;
  min-height: 1040rpx;
  margin: 120rpx 0 0 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.block_1 {
  width: 150rpx;
  height: 88rpx;
  margin-left: 32rpx;
  align-items: center;
}

.box_3 {
  background-color: rgba(31, 115, 255, 1);
  width: 8rpx;
  height: 32rpx;
  margin-top: 28rpx;
}

.text_7 {
  width: 128rpx;
  height: 88rpx;
  overflow-wrap: break-word;
  color: rgba(26, 32, 44, 1);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  line-height: 88rpx;
}

.text-wrapper_1 {
  width: 164rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
  margin: 16rpx 0 0 30rpx;
}

.text_8 {
  color: rgba(236, 48, 48, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
}

.text_9 {
  color: rgba(22, 23, 26, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
}

.block_2 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8px;
  height: 88rpx;
  width: 638rpx;
  margin: 12rpx 0 0 32rpx;
}

.text_10 {
  width: 100%;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(22, 23, 26, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 56rpx;
  margin: 16rpx 0 0 22rpx;
  border: none;
  background: transparent;
  outline: none;
}

.text_10::placeholder {
  color: rgba(177, 181, 188, 1);
}

.text_10.error {
  border: 2rpx solid rgba(236, 48, 48, 1);
}

.text-wrapper_3 {
  width: 136rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
  margin: 52rpx 0 0 30rpx;
}

.text_11 {
  color: rgba(236, 48, 48, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
}

.text_12 {
  color: rgba(22, 23, 26, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
}

.block_3 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8px;
  height: 88rpx;
  width: 638rpx;
  margin: 12rpx 0 0 32rpx;
}

.text_13 {
  width: 100%;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(22, 23, 26, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 56rpx;
  margin: 16rpx 0 0 22rpx;
  border: none;
  background: transparent;
  outline: none;
}

.text_13::placeholder {
  color: rgba(177, 181, 188, 1);
}

.text_13.error {
  border: 2rpx solid rgba(236, 48, 48, 1);
}

.text-wrapper_5 {
  width: 164rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
  margin: 52rpx 0 0 30rpx;
}

.text_14 {
  color: rgba(236, 48, 48, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
}

.text_15 {
  color: rgba(22, 23, 26, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
}

.text-wrapper_6 {
  background-image: linear-gradient(
    90deg,
    rgba(49, 120, 249, 1) 0,
    rgba(51, 197, 255, 1) 100%
  );
  border-radius: 22px;
  height: 88rpx;
  width: 638rpx;
  margin: 292rpx 0 32rpx 32rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
}

.text-wrapper_6:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(49, 120, 249, 0.3);
}

.text-wrapper_6:active {
  transform: translateY(0);
}

.text_16 {
  color: rgba(255, 255, 255, 1);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}

.text-wrapper_7 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8px;
  position: absolute;
  left: 32rpx;
  top: 544rpx;
  width: 638rpx;
  height: 328rpx;
  padding: 24rpx 22rpx;
}

.text_17 {
  width: 100%;
  height: 200rpx;
  overflow-wrap: break-word;
  color: rgba(22, 23, 26, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  border: none;
  background: transparent;
  outline: none;
  resize: none;
}

.text_17::placeholder {
  color: rgba(177, 181, 188, 1);
}

.text_17.error {
  border: 2rpx solid rgba(236, 48, 48, 1);
}

.text_18 {
  width: 70rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: right;
  line-height: 56rpx;
  position: absolute;
  bottom: 24rpx;
  right: 22rpx;
}

/* 错误提示 */
.error-text {
  color: rgba(236, 48, 48, 1);
  font-size: 24rpx;
  margin-top: 8rpx;
  margin-left: 32rpx;
}

/* 温馨提示区域 */
.group_6 {
  width: 750rpx;
  margin: 40rpx 0;
  padding: 0 24rpx;
}

.box_4 {
  width: 126rpx;
  height: 48rpx;
  align-items: center;
  margin-bottom: 20rpx;
}

.thumbnail_1 {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.text_19 {
  color: rgba(236, 139, 48, 1);
  font-size: 24rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  line-height: 48rpx;
}

.paragraph_1 {
  width: 100%;
  overflow-wrap: break-word;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 48rpx;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 底部导航 */
.group_7 {
  background-color: rgba(255, 255, 255, 1);
  position: fixed;
  left: 0;
  bottom: 0;
  width: 750rpx;
  height: 166rpx;
  z-index: 1000;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.image_3 {
  width: 750rpx;
  height: 2rpx;
}

.list_1 {
  width: 740rpx;
  height: 80rpx;
  margin: 14rpx 0 0 4rpx;
}

.image-text_3 {
  width: 188rpx;
  height: 80rpx;
  margin-right: -4rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-text_3:hover {
  transform: translateY(-2rpx);
}

.label_3 {
  width: 48rpx;
  height: 48rpx;
  margin-left: 70rpx;
}

.text-group_3 {
  width: 188rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 4rpx;
}

.image_4 {
  width: 750rpx;
  height: 68rpx;
  margin-top: 2rpx;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bank-picker {
  background-color: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 800rpx;
  overflow: hidden;
}

.picker-header {
  padding: 40rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
}

.picker-list {
  max-height: 600rpx;
}

.picker-item {
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
  cursor: pointer;
}

.picker-item:hover {
  background-color: #f8f9fa;
}

.bank-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.bank-contact {
  font-size: 24rpx;
  color: #666;
}
