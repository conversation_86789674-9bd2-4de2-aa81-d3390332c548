module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://kjrzymt.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/api': '/api'
        },
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cache-Control, Pragma'
        },
        onProxyReq: (proxyReq, req, res) => {
          // 添加防缓存头
          proxyReq.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
          proxyReq.setHeader('Pragma', 'no-cache');
          proxyReq.setHeader('Expires', '0');
          // 移除可能导致缓存的头
          proxyReq.removeHeader('If-Modified-Since');
          proxyReq.removeHeader('If-None-Match');
        }
      }
    }
  },
  transpileDependencies: ['@dcloudio/uni-ui']
} 