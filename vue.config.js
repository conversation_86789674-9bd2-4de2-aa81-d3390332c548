module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://kjrzymt.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/api': '/api'
        },
        timeout: 30000, // 增加超时时间
        followRedirects: true,
        onProxyReq: (proxyReq, req) => {
          // 设置完整的浏览器请求头来绕过防护
          proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
          proxyReq.setHeader('Accept', 'application/json, text/plain, */*');
          proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
          proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
          proxyReq.setHeader('Connection', 'keep-alive');
          proxyReq.setHeader('Referer', 'http://kjrzymt.com/');
          proxyReq.setHeader('Origin', 'http://kjrzymt.com');
          proxyReq.setHeader('Sec-Fetch-Dest', 'empty');
          proxyReq.setHeader('Sec-Fetch-Mode', 'cors');
          proxyReq.setHeader('Sec-Fetch-Site', 'same-origin');
          proxyReq.setHeader('Sec-Ch-Ua', '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"');
          proxyReq.setHeader('Sec-Ch-Ua-Mobile', '?0');
          proxyReq.setHeader('Sec-Ch-Ua-Platform', '"Windows"');

          // 添加防缓存头
          proxyReq.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
          proxyReq.setHeader('Pragma', 'no-cache');
          proxyReq.setHeader('Expires', '0');

          // 如果是POST请求，确保Content-Type正确
          if (req.method === 'POST') {
            proxyReq.setHeader('Content-Type', req.headers['content-type'] || 'application/json');
          }

          // 移除可能导致缓存的头
          proxyReq.removeHeader('If-Modified-Since');
          proxyReq.removeHeader('If-None-Match');

          console.log(`代理请求: ${req.method} ${proxyReq.path}`);
        },
        onProxyRes: (proxyRes, req, res) => {
          console.log(`代理响应: ${proxyRes.statusCode} ${req.url}`);

          // 处理响应头
          proxyRes.headers['Access-Control-Allow-Origin'] = '*';
          proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
          proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Cache-Control, Pragma';

          // 如果返回的是HTML（可能是防护页面），记录日志
          if (proxyRes.headers['content-type'] && proxyRes.headers['content-type'].includes('text/html')) {
            console.warn('警告: 服务器返回HTML内容，可能被防护系统拦截');
          }
        },
        onError: (err, req, res) => {
          console.error('代理错误:', err.message);
          res.writeHead(500, {
            'Content-Type': 'text/plain'
          });
          res.end('代理服务器错误: ' + err.message);
        }
      }
    }
  },
  transpileDependencies: ['@dcloudio/uni-ui']
}