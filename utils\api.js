// API接口配置
const BASE_URL = process.env.NODE_ENV === 'development' ? '/api' : 'http://kjrzymt.com/api'  // 开发环境使用代理，生产环境使用实际地址

// 模拟数据函数
const getMockData = (url, method) => {
  // 根据URL返回对应的模拟数据
  if (url.includes('/statistics')) {
    return {
      policy_count: 156,
      bank_count: 48,
      faq_count: 89,
      interpretation_count: 67
    }
  }
  
  if (url.includes('/banners')) {
    return [
      {
        id: 1,
        title: '重庆跨境融资政策解读',
        image: '/static/images/banner/cq-jiefangbei.jpg',
        link_type: 'policy',
        link_id: 1
      }
    ]
  }
  
  if (url.includes('/banks')) {
    return {
      total: 48,
      data: [
        {
          id: 1,
          name: '中国银行重庆分行',
          contact_person: '张经理',
          phone: '023-********',
          address: '重庆市渝中区邹容路68号'
        },
        {
          id: 2, 
          name: '工商银行重庆分行',
          contact_person: '李经理',
          phone: '023-********',
          address: '重庆市渝中区民族路188号'
        }
      ]
    }
  }
  
  if (url.includes('/news')) {
    return {
      has_next: true,
      has_prev: false,
      items: [
        {
          id: 1,
          title: '重庆跨境融资业务规模突破千亿元',
          category: '政策动态',
          view_count: 3250,
          publish_date: '2024-01-15T10:30:00',
          cover_img: 'https://example.com/news1.jpg'
        },
        {
          id: 2,
          title: '多家银行推出跨境融资新产品',
          category: '产品创新',
          view_count: 2890,
          publish_date: '2024-01-14T14:20:00',
          cover_img: 'https://example.com/news2.jpg'
        }
      ],
      page: 1,
      pages: 2,
      per_page: 5,
      total: 7
    }
  }
  
  if (url.includes('/faqs')) {
    return {
      has_next: true,
      has_prev: false,
      items: [
        {
          id: 6,
          question: '这是测试问题？',
          answer: '这是测试答案',
          category: '测试分类',
          answer_date: '2025-06-22',
          collect_count: 1,
          created_at: '2025-06-22T10:22:49',
          forward_count: 0,
          like_count: 1,
          view_count: 1
        },
        {
          id: 7,
          question: '企业如何防范跨境融资汇率风险？',
          answer: '企业防范跨境融资汇率风险的主要措施：<br/><br/><strong>1. 自然对冲</strong><br/>• 资产负债币种匹配<br/>• 收支币种匹配<br/>• 期限结构匹配<br/><br/><strong>2. 金融工具对冲</strong><br/>• 外汇远期<br/>• 外汇期权<br/>• 货币互换<br/><br/><strong>3. 操作策略</strong><br/>• 分批融资<br/>• 动态调整<br/>• 专业团队管理<br/><br/><strong>4. 政策工具</strong><br/>• 跨境人民币融资<br/>• 本币结算<br/>• 政策性保险',
          category: '风险管理',
          answer_date: '2024-01-30',
          collect_count: 0,
          created_at: '2025-06-22T09:29:49',
          forward_count: 0,
          like_count: 0,
          view_count: 0
        }
      ],
      page: 1,
      pages: 2,
      per_page: 5,
      total: 7
    }
  }
  
  if (url.includes('/policies')) {
    return {
      has_next: true,
      has_prev: false,
      items: [
        {
          id: 1,
          title: '重庆市跨境融资业务管理办法',
          summary: '为规范重庆市跨境融资业务，促进跨境资金流动便利化，支持实体经济发展，制定本办法。本办法适用于在重庆市行政区域内开展的跨境融资业务。',
          category: '管理办法',
          view_count: 2150,
          download_count: 156,
          rating: 4.8,
          publish_date: '2024-01-15T10:30:00'
        },
        {
          id: 2,
          title: '关于支持重庆跨境融资发展的指导意见',
          summary: '为进一步发挥重庆在跨境融资中的重要作用，支持企业"走出去"和"引进来"，促进重庆经济高质量发展，现提出以下指导意见。',
          category: '指导意见',
          view_count: 1890,
          download_count: 98,
          rating: 4.6,
          publish_date: '2024-01-14T14:20:00'
        }
      ],
      page: 1,
      pages: 2,
      per_page: 5,
      total: 7
    }
  }
  
  if (url.includes('/interpretations')) {
    return {
      has_next: true,
      has_prev: false,
      items: [
        {
          id: 1,
          title: '重庆跨境融资政策解读：申请流程详解',
          category: '申请流程',
          view_count: 1850,
          like_count: 89,
          comment_count: 23,
          video_url: 'https://example.com/video1.mp4',
          created_at: '2024-01-15T10:30:00'
        },
        {
          id: 2,
          title: '跨境融资额度计算方法解析',
          category: '额度管理',
          view_count: 1620,
          like_count: 67,
          comment_count: 18,
          video_url: null,
          created_at: '2024-01-14T14:20:00'
        }
      ],
      page: 1,
      pages: 2,
      per_page: 5,
      total: 7
    }
  }
  
  // 默认返回空数据
  return {
    total: 0,
    data: []
  }
}

// 请求封装
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 设置超时时间
    const timeout = setTimeout(() => {
      reject(new Error('请求超时'))
    }, 10000) // 10秒超时

    const requestUrl = BASE_URL + options.url
    console.log('发送请求:', {
      url: requestUrl,
      method: options.method || 'GET',
      data: options.data || {},
      headers: {
        'Content-Type': 'application/json',
        'Authorization': uni.getStorageSync('token') ? 'Bearer ' + uni.getStorageSync('token') : '',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'If-Modified-Since': '',
        'If-None-Match': '',
        ...options.header
      }
    })
    
    uni.request({
      url: requestUrl,
      method: options.method || 'GET',
      data: options.data || {},
      timeout: 10000, // 10秒超时
      header: {
        'Content-Type': 'application/json',
        'Authorization': uni.getStorageSync('token') ? 'Bearer ' + uni.getStorageSync('token') : '',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'If-Modified-Since': '',
        'If-None-Match': '',
        ...options.header
      },
      success: (res) => {
        clearTimeout(timeout)
        
        if (res.statusCode === 200) {
          if (res.data && (res.data.success || res.data.code === 200)) {
            resolve(res.data)
          } else {
            // 模拟数据用于开发测试
            console.log('API返回异常，使用模拟数据')
            resolve({
              success: true,
              message: 'success',
              data: getMockData(options.url, options.method)
            })
          }
        } else {
          console.log('网络请求失败，使用模拟数据')
          resolve({
            success: true,
            message: 'success',
            data: getMockData(options.url, options.method)
          })
        }
      },
      fail: (err) => {
        clearTimeout(timeout)
        console.log('网络连接失败，使用模拟数据:', err)
        
        // 在开发阶段使用模拟数据
        resolve({
          code: 200,
          message: 'success',
          data: getMockData(options.url, options.method)
        })
      }
    })
  })
}

// API接口定义
export const api = {
  // ===================
  // 🔍 搜索接口
  // ===================
  search: (params) => request({
    url: '/search/',
    method: 'GET',
    data: params
  }),

  // ===================
  // 📊 统计数据接口
  // ===================
  getStatistics: () => request({
    url: '/statistics/',
    method: 'GET'
  }),

  // ===================
  // 🎨 轮播图接口
  // ===================
  getBanners: () => request({
    url: '/banners/',
    method: 'GET'
  }),

  // ===================
  // 🏦 银行机构接口
  // ===================
  getBanks: (params) => request({
    url: '/banks/',
    method: 'GET',
    data: params
  }),

  getBankDetail: (id) => request({
    url: `/banks/${id}`,
    method: 'GET'
  }),

  // ===================
  // 📰 新闻接口
  // ===================
  getNews: (params) => request({
    url: '/news/',
    method: 'GET',
    data: params
  }),

  getNewsDetail: (id) => request({
    url: `/news/${id}`,
    method: 'GET'
  }),

  getNewsCategories: () => request({
    url: '/news/categories',
    method: 'GET'
  }),

  // ===================
  // ❓ 热门问题接口
  // ===================
  getFaqs: (params) => request({
    url: '/faqs/',
    method: 'GET',
    data: params
  }),

  getFaqDetail: (id) => request({
    url: `/faqs/${id}`,
    method: 'GET'
  }),

  // ===================
  // 📋 政策文件接口
  // ===================
  getPolicies: (params) => request({
    url: '/policies/',
    method: 'GET',
    data: params
  }),

  getPolicyDetail: (id) => request({
    url: `/policies/${id}`,
    method: 'GET'
  }),

  // ===================
  // 🎥 政策解读接口
  // ===================
  getInterpretations: (params) => request({
    url: '/interpretations/',
    method: 'GET',
    data: params
  }),

  getInterpretationDetail: (id) => request({
    url: `/interpretations/${id}`,
    method: 'GET'
  }),

  // ===================
  // 💌 咨询提交接口
  // ===================
  submitInquiry: (data) => request({
    url: '/inquiries/',
    method: 'POST',
    data: data
  }),

  // ===================
  // 📈 用户互动记录接口
  // ===================
  // 记录浏览
  recordView: (data) => request({
    url: '/interactions/view',
    method: 'POST',
    data: data
  }),

  // 切换互动状态（点赞/转发）
  toggleInteraction: (data) => request({
    url: '/interactions/toggle',
    method: 'POST',
    data: data
  }),

  // ===================
  // 💖 收藏接口
  // ===================
  // 切换收藏状态
  toggleCollection: (data) => request({
    url: '/collections/toggle',
    method: 'POST',
    data: data
  }),

  // 获取用户收藏列表
  getMyCollections: (params) => request({
    url: '/collections/my',
    method: 'GET',
    data: params
  }),

  // 检查收藏状态
  checkCollectionStatus: (data) => request({
    url: '/collections/check',
    method: 'POST',
    data: data
  }),

  // 获取用户咨询列表
  getMyInquiries: (userId, params) => request({
    url: `/inquiries/user/${userId}`,
    method: 'GET',
    data: params
  }),
  
  // 获取我的互动记录
  getMyInteractions: (params) => request({
    url: '/interactions/my',
    method: 'GET',
    data: params
  }),

  // 检查互动状态
  checkInteractionStatus: (data) => request({
    url: '/interactions/check',
    method: 'POST',
    data: data
  }),

  // ===================
  // 👤 认证接口
  // ===================
  // 微信登录
  wechatLogin: (data) => request({
    url: '/auth/wechat/login',
    method: 'POST',
    data: data
  }),

  // 获取用户资料
  getUserProfile: (params) => request({
    url: '/auth/profile',
    method: 'GET',
    data: params
  }),

  // ===================
  // 向下兼容的旧接口
  // ===================
  // 保持向下兼容
  recordInteraction: (data) => {
    // 根据数据类型调用不同的新接口
    if (data.action === 'view') {
      return api.recordView(data)
    } else {
      return api.toggleInteraction(data)
    }
  }
}

export default api 